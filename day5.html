<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
  </head>
  <body>
    <button onclick="getSymbol('+')">+</button>
    <button onclick="getSymbol('-')">-</button>
    <button onclick="getSymbol('/')">/</button>
    <button onclick="getName()">get name</button>

    <button onclick="getUpdate()" id="btn">update</button>
    <!-- <button onmouseover="changeBtn()" id="btn2">mouse over</button>
      -->

    <div id="display"></div>

    <div>
      <input type="text" id="textInp" />
      <button onclick="getVal()">display</button>
      <p id="displayText"></p>
    </div>
  </body>
  <script>
    // Dom & events in javascript
    //1. Document Object Model (DOM)
    // The DOM is a programming interface that represent an HTML document as a tree structure where elements are nodes. Javascript can manipulate and dynamically change content, structure and styles.

    //2. Events in javascript
    // Events allow you to execute code when users interacts with a webpage (clicking, typing, hovering etc...)
    function getSymbol(symbol) {
      console.log(symbol);
    }

    function getName() {
      console.log("get name");
    }

    function getVal(){
        let inp = document.getElementById('textInp').value;
        let display = document.getElementById('displayText');
        console.log(inp, display);
        //  to show on webpage
        display.innerHTML += `${inp} <br>`
        document.getElementById('textInp').value = " "
    };

    let userName = 'Mercy';
    function getUpdate(){
        // let btn = document.querySelector('#btn')
        let btn = document.getElementById('btn')
        console.log(btn);
        let display = document.getElementById('display')
        console.log(display);

        let p = document.createElement('p');
            p.innerHTML = `good morning ${userName}`

            display.appendChild(p)



            // display.innerHTML = `Good morning ${userName}`


            // btn.textContent = 'yellow';
            // btn.style.backgroundColor = 'green';
            // btn.style.padding = '10px 20px';
            // btn.style.borderRadius = '10px';
            // btn.style.border = 'none';

            // document.body.style.backgroundColor = 'crimson';


    }


    // function changeBtn() {
    //     let btn = document.getElementById('btn2');
    //     console.log(btn);

    //     document.body.style.backgroundColor = 'black';

    // }

  </script>
</html>
