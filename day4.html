<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <button onclick="getUpdate()">click me</button>
    <script>
        // Function -- are a block of code for performing a specific task;
        //a function must be named, followed by a (), and a {}
        //Named function -- which always declare by using the keyword 'function';

        function gold(){
            console.log('Gold is a baby man-shit fan');
        };
        gold() //invoking the function 

        function getUpdate (){
            setTimeout(()=> {
                console.log(`you will recieve update after 10s`);
            }, 10000)
        } 


        //Arrow function -- is a concise way of writing a function using your fat arrow ()=>{}

        const greetMe = ()=> {
            console.log(`keep dreaming`);
        }

        setTimeout(()=> { //callback function
            greetMe()
        }, 10000)


        //Global scope and local scope

        let username = 'Mercy'; //global scope var

        function getUsername(){
            username = 'Fred'
            console.log(`current username is ${username}`);

            let newUsername ='Tolu';// local scope var
            console.log(newUsername);
            
        }
        getUsername();

        console.log(username);

        newUsername = 'Gold';
        console.log(newUsername);
        
        //console.log(newUsername);


        //Assignment
        //1. write a function that checks for the eligibility of a citizen that can vote, marry and drive once their age is 18 and above.
        //2.write a function that returns the value of a sum of two numbers 

        
        //assignment 1 
        function checkEligibility() {
            let age = 17;
            if (age >= 18) {
                console.log(`you are eligible`);
            }else {
                console.log(`you are not eligible`);
            }
        }
        checkEligibility()
        
        //assignment 2 
        function getSum(){
            let sum1 = 9;
            let sum2 = 8;

            console.log(sum1 + sum2);
        }
        getSum()
        
        

    </script>
</body>
</html>