<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <input oninput="display()" type="text" id="inp">
    <p id="dis"></p>

    <script>
        function display() {
            let textInp = document.getElementById("inp").value;
        //    console.log(textInp);
            let dis = document.getElementById("dis");
            // console.log(dis);
            dis.innerHTML = `${textInp}`
        }

    </script>
</body>
</html>