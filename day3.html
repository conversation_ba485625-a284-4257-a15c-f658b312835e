<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
  </head>
  <body>
    <script>
      let robot = {
        walking: function () {
          console.log(`i am walking`);
        },
        dance: function () {
          console.log(`i am dancing`);
        },
        talk: function () {
          console.log(`i am talking`);
        },
      };

      console.log(robot.walking());

      //Number Methods

      //To convert numbers to string, we can use the following methods

      let num = 345.6;
      console.log(num);

      //toString -- converts number to string
      console.log(num.toString());

      //toFixed -- round up decimals to the nearest whole number and still convert to strings
      console.log(num.toFixed());

      //toExponential
      console.log(num.toExponential);

      //toPrecision
      console.log(num.toPrecision);

      //To convert string to number, we can use the following methods

      let str = "345.6";
      console.log(str);

      //Number()
      console.log(Number(str));

      //parseFloat()
      console.log(parseFloat(str));

      //parseInt()
      console.log(parseInt(str));

      //using addition operator +
      console.log(+str);

      //Math Methods

      //Math.ceil
      console.log(Math.ceil(34));

      //Math.floor
      console.log(Math.floor(890.56));

      //Math.random
      console.log(Math.random(3456789) + 1);
      console.log(Math.random() * 23455678);

      //Math.round
      console.log(Math.round(345.67));

      //Math.sqrt
      console.log(Math.sqrt(16));

      //operators
      //1. Arithemetic operator--- they are your +,-,/,*
      //2. Assignment operator-- which are used to assign a vlue to variable
      //3. Addition and subtraction assignment operator i.e pre increment, post increment, pre decrement, post decrement
      //4. Comparison operator--- they are use to compare such as >,<. >=, <=, ==, ===, !=, !==
      //5. Logical operator-- they are for logical purposes such as &&, AND, ||
      //6. Ternary operator
      //7. Conditional statement

      //1. Arithemetic operator
      let digital1 = 10;
      let digital2 = 20;

      //addition operator +
      let digitalSum = digital1 + digital2;
      console.log(digitalSum);

      //sub
      digitalSum = digital2 - digital1;
      console.log(digitalSum);

      //div

      digitalSum = digital2 / digital1;
      console.log(digitalSum);

      //multi
      digitalSum = digital2 * digital1;
      console.log(digitalSum);

      //expo
      digitalSum = digital1 ** 2;
      console.log(digitalSum);

      //3. addition assignment operator

      digital1 += digital1; // digital1 + digital1+ digital1
      console.log(digital1);

      digital1 -= digital2;
      console.log(digital1);

      //increment

      let a = 1;
      let b = a++; //post increment
      console.log(b);

      let c = ++a; //pre increment
      console.log(a);

      let d = c--; // post decremrnt
      console.log(d);

      let e = --d; // pre decrement
      console.log(e);

      let f = --e;
      console.log(f);

      // Compaison Operator

      let numVal1 = 10;
      let numVal2 = "10";
      let numVal = 20;
      let newVal = "20";

      console.log(numVal > numVal2);
      console.log(numVal1 >= numVal2);
      console.log(numVal1 <= numVal);
      console.log(numVal1 >= numVal2);

      console.log(true == true);
      console.log(numVal1 == numVal2); // check for equal value
      console.log(numVal1 === numVal2); // check for value and datatypes

      console.log(numVal != numVal);
      console.log(numVal1 != numVal2);
      console.log(numVal1 !== numVal2);

      console.log(true == true >= numVal1 < newVal);
      console.log(false >= false != numVal1 >= numVal2);

      //local operator..&&, ||

      console.log(true && true);
      console.log(true && false);
      console.log(false && true);
      console.log(false && false);

      console.log(true || true);
      console.log(true || false);
      console.log(false || true);
      console.log(false || false);

      console.log((true && false) || false || (false && true));
      console.log(false || (true && false && true) || false);
      console.log((true && true) || (true >= false && false) || 1);

      //Ternary operator

      let age = 18;
      let isOldEnough;
      isOldEnough =
        age && 10
          ? "yes he's old enough to vote"
          : "he is not old enough to vote";
      console.log(isOldEnough);

      //Conditional statement

      if ((age += 18)) {
        console.log("yes");
      } else {
        console.log("no");
      }

      let color = "green";

      if (color == "Green") {
        console.log("valid");
      } else if (color == "green") {
        console.log("i am green");
      } else {
        console.log("invalid");
      }

      let car = true;
      let machine;

      if (car && machine) {
        console.log("i do not exist");
      } else {
        console.log("i am existing");
      }

    </script>
  </body>
</html>
