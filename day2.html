<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <script>
        //Data Restructuring
        //Destructuring is a convenient way of extracting multiple values from data stored in objects and arrays.
        // It allows us to unpack values from arrays or properties from objects into distinct variables.
         
        let userObj = {
            firstname: '<PERSON>',
            lastname: 'Baptist',
            gender: 'male',
            role: 'Disciple',
            DOB: new Date().toLocaleDateString()
        }
 
        console.log(userObj);
        console.log(userObj.firstname);


        let { firstname, lastname, gender, role, DOB } = userObj;
        console.log(firstname);
        console.log(lastname);
        console.log(gender);
        console.log(role);
        console.log(DOB);




        //Example with Array Destructuring
        const fruits = ['apple', 'banana', 'cherry'];
        const [first, second, third] = fruits;
        console.log(first); // apple
        console.log(second); // banana
        console.log(third); // cherry

        //Example with Object Destructuring
        const person = {
            name: '<PERSON>',
            age: 30,
            city: 'New York'
        };
        const { name, age, city } = person;
        console.log(name); // John
        console.log(age); // 30
        console.log(city); // New York



        //methods are functions that are properties of an object. they can be used to manipulate data types.

        //string methods

        let str = 'bolaji';

        //1. length -- returns the length of a string
        console.log(str.length);

        //2. toUpperCase() -- converts a string to uppercase
        console.log(str.toUpperCase());

        //3. toLowerCase() -- converts a string to lowercase
        console.log(str.toLowerCase());

        //4. indexOf() -- returns the index of the first occurrence of a specified value in a string
        console.log(str.indexOf('a')); // returns 3

        //5. lastIndexOf() -- returns the index of the last occurrence of a specified value in a string
        console.log(str.lastIndexOf('a')); // returns 3

        //6. slice() -- extracts a section of a string and returns it as a new string
        console.log(str.slice(0, 3)); // returns 'bol'

        //7. substring() -- returns a part of the string between two specified indices
        console.log(str.substring(1, 4)); // returns 'ola'

        //8. replace() -- replaces a specified value with another value in a string
        console.log(str.replace('bol', 'b')); // returns 'baji'

        //9. split() -- splits a string into an array of substrings
        console.log(str.split('a')); // returns ['bol', 'ji']

        //10. trim() -- removes whitespace from both ends of a string
        console.log(str.trim()); // returns 'bolaji' (no change in this case)
        
        //11. charAt() -- returns the character at a specified index
        console.log(str.charAt(2)); // returns 'l'
    </script>
</body>
</html>