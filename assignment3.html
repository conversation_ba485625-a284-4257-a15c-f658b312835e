<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <input type="text" id="inp1">
      <select name="operator" id="operator">
        <option value="+">+</option>
        <option value="-">-</option>
        <option value="x">x</option>
        <option value="÷">÷</option>
        <option value="%">%</option>
    </select>
    <input type="text" id="inp2">
    <button onclick="calculate()">Calculate</button>
    <input type="text" id="result">

    <p id="dis"></p>
  
    <script>
        function calculate() {
            let num1 = document.getElementById('inp1').value;
            let num2 = document.getElementById('inp2').value;
            let operator = document.getElementById('operator').value;
            let result = document.getElementById('result');
            console.log(num1, num2); 

            if (operator == '+') {
                result.value = parseFloat(num1) + parseFloat(num2);
            }
            else if (operator == '-') {
                result.value = parseFloat(num1) - parseFloat(num2);
            }
            else if (operator == 'x') {
                result.value = parseFloat(num1) * parseFloat(num2);
            }
            else if (operator == '÷') {
                result.value = parseFloat(num1) / parseFloat(num2);
            }
            else if (operator == '%') {
                alert('invalid') 
            }
            else {
                alert('invalid')
            }

            let dis = document.getElementById("dis");
               if (dis.innerHTML === "") {
                dis.innerHTML = result.value;
            } else {
                dis.innerHTML += ` ${result.value}`;
                dis.innerHTML = parseFloat(dis.innerHTML) + parseFloat(result.value); 
            }

            document.getElementById('inp1').value = '';
            document.getElementById('inp2').value = '';
        }
    </script>

</body>
</html>