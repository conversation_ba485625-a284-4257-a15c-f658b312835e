<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <input type="number" id="num1">
   <input type="text" id="operator">
    <input type="number" id="num2">
    <button onclick="calculate()">Calculate</button>
    <input type="text" id="result">

    <script>
        function calculate(){
            let num1 = document.getElementById('num1').value;
            let num2 = document.getElementById('num2').value;
            let operator = document.getElementById('operator').value;
            let result = document.getElementById('result');
            console.log(num1, num2);

          if (operator=='+') {
            let res = (parseFloat(num1) + parseFloat(num2));
            result.value = res;
          }
          else if (operator=='-') {
            res = (parseFloat(num1) - parseFloat(num2));
            result.value = res;
          }
          else if (operator=='/') {
            res = (parseFloat(num1) / parseFloat(num2));
            result.value = res;
          }
          else if (operator=='x') {
            res = (parseFloat(num1) * parseFloat(num2));
            result.value = res;
          }
          else{
            console.log('empty');
          }
           document.getElementById('num1').value = '';
           document.getElementById('num2').value = '';
        }
    </script>
</body>
</html>