<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <script>
        //VARIABLE --are containers for storing information.
        //different ways of naming variables 
        //pascal case, camel case, kebab case, snake case etc.
        // camelCase -- this is the best method of naming variables in js. e.g myName, myAudio etc.

        //  we have three ways of declaring variables
        // 1. let -- we can reassign but not redeclare in the same scope

        let myName = 'Fred'; //Declare
        console.log(myName);

        myName = 'jason'; //Reassign

        //2. const -- we cannot redeclare neither can we reassign

        const pi = 3.142;
        console.log(pi);

        const meatPie = 'Pie Express';
        console.log(meatPie);

        //DATA TYPES
        // two different types of data types
        // 1. Primitive Data Types: are immutable data types
        // a. string -- are characters or numbers which are wrapped in quotes.

        let str = 'Fred';
        console.log(str);

        str = '3.134';
        console.log(str);
        console.log(typeof str);

        str = myName + 45;
        console.log(str);

        //b. number -- are digits, decimals, integers, floats, odd or even numbers.

        let num = 3.45;
        console.log(num);
        console.log(typeof num);

        let num2 = '3.45';
        
        //Concatenation
        let myDetails = 'my name is ' + myName + ',' + 'i am' + ' ' + num + ' years old';
        console.log(myDetails);

        //template literal -- we make use of backtick 

        num = 270;
        
        let state = 'oyo state';
        let homeTown = 'ibadan';

        myDetails = `my name is ${myName}, i am ${num} years of age. i am from ${state}, and i live at ${homeTown}`;
        console.log(myDetails);

        //Boolean --- returns true or false

        let hisAlive;
        console.log(hisAlive);
        console.log(typeof hisAlive);

        let status = hisAlive = true ? 'yes, his alive' : 'no his dead';
        console.log(status);
        
        // Null -- empty / void

        let user1 = null;
        console.log(user1);

        user1 = 'gold';
        console.log(user1);

        user1 = '';
        console.log(user1);

        // Undefined -- this is when a variable is declared without a value

        let myUsers;
        console.log(myUsers);
        console.log(typeof myUsers);

        myUsers = 'Tobi, Fred, Mercy';
        console.log(myUsers);

        myUsers = "My name is fred. i am 100 years old and i have a friend and he's a man"


        //2. Non Primitive data types -- mutable

        //a. Object- are key-pair values

        let obj = {
            name: 'Fred',
            age: 100,
            isAlive: true,
            gender: 'male',
            address: {
                state: 'Oyo',
                city: 'Ibadan',
                street: 'Bodija'
            }
        }
        console.log(obj);
        console.log(typeof obj);

        // Accessing object properties (Dot Notation and Bracket Notation)

        console.log(obj.name); //Dot Notation
        console.log(obj.address.city); //Dot Notation
        console.log(obj['age']); //Bracket Notation
        console.log(obj['address']['state']); //Bracket Notation

        console.log(obj.name);
        console.log(obj.age);
        console.log(obj.address.state);


        //nested object
        let nest = {
            addresses: {
                state:{
                    city: {
                        street: 'Bodija'
                    }
                }
            }
        }
        console.log(nest.addresses.state.city.street);

        //b. Array -- are list of items or collection of values. They are bigger than objects and can hold multiple values.

        let myArr = ['Benz', 'Toyota', 'Honda', 'Kia', 'Lexus', 'Ford', 'BMW'];
        console.log(myArr);
        console.log(typeof myArr);

        console.log(myArr.length); //length of the array
        console.log(myArr[0]); //Accessing the first item in the array
        console.log(myArr[myArr.length - 1]); //Accessing the last item
        console.log(myArr[3]); //Accessing the fourth item in the array

        //Adding items to the array
        myArr.push('Mercedes'); //adds to the end of the array
        console.log(myArr);
        myArr.unshift('Audi'); //adds to the beginning of the array
        console.log(myArr);
        myArr.splice(3, 1, 'Porsche'); //replaces 1 item at index 3
        console.log(myArr);

        myArr.push(obj); 
        console.log(myArr); 

        myArr.push(['pawpaw', 'mango', 'melon'], 'Nissan', 'GT-R');  
        console.log(myArr);

        // Corrected Array Access Examples
        // The object 'obj' is at index 9 after all the push, unshift, and splice operations.
        console.log(myArr[9].name);
        console.log(myArr[9].address.city);


        //Removing items from the array
        myArr.pop(); //removes the last item in the array
        console.log(myArr);
        myArr.shift(); //removes the first item in the array
        console.log(myArr);
        myArr.splice(2, 1); //removes the third item in the array
        console.log(myArr);
        myArr.splice(3, 1, 'Tesla'); //replaces the fourth item in the array
        console.log(myArr);
       
         
        
    </script>
</body>
</html>