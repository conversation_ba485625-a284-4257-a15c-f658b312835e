<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Calculator</title>
    <style>
      body {
        margin: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background-color: white;
      }

      .calculator {
        width: 280px;
        height: 500px;
        background-color: #000;
        border-radius: 20px;
        padding: 20px;
        display: flex;
        flex-direction: column;
      }

      .display-area {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: flex-end;
        padding: 0 10px 10px;
      }

      .display {
        font-size: 48px;
        color: #fff;
        font-weight: 300;
        text-align: right;
        min-height: 50px;
      }

      .result-display {
        font-size: 24px;
        color: #aaa;
        font-weight: 300;
        text-align: right;
      }

      .button-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 10px;
      }

      .calc-btn {
        width: 60px;
        height: 60px;
        border: none;
        border-radius: 50%;
        background-color: #333;
        color: #fff;
        font-size: 24px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .calc-btn:hover {
        background-color: #555;
      }

      .calc-btn:active {
        background-color: #777;
      }

      .special {
        background-color: #a5a5a5;
        color: #000;
      }

      .special:hover {
        background-color: #c5c5c5;
      }

      .special:active {
        background-color: #e5e5e5;
      }

      .operator {
        background-color: #f1a33c;
      }

      .operator:hover {
        background-color: #f3b868;
      }

      .operator:active {
        background-color: #f5c98e;
      }

      .zero {
        grid-column: span 2;
        border-radius: 30px;
        width: 130px;
        justify-content: flex-start;
        padding-left: 22px;
      }
    </style>
  </head>
  <body>
    <div class="calculator">
      <div class="display-area">
        <div class="result-display" id="result-display"></div>
        <div class="display" id="display"></div>
      </div>

      <div class="button-grid">
        <button class="calc-btn special" onclick="show('AC')">AC</button>
        <button class="calc-btn special" onclick="show('C')">C</button>
        <button class="calc-btn special" onclick="show('%')">%</button>
        <button class="calc-btn operator" onclick="show('÷')">÷</button>
        <button class="calc-btn" onclick="show('7')">7</button>
        <button class="calc-btn" onclick="show('8')">8</button>
        <button class="calc-btn" onclick="show('9')">9</button>
        <button class="calc-btn operator" onclick="show('×')">×</button>
        <button class="calc-btn" onclick="show('4')">4</button>
        <button class="calc-btn" onclick="show('5')">5</button>
        <button class="calc-btn" onclick="show('6')">6</button>
        <button class="calc-btn operator" onclick="show('-')">-</button>
        <button class="calc-btn" onclick="show('1')">1</button>
        <button class="calc-btn" onclick="show('2')">2</button>
        <button class="calc-btn" onclick="show('3')">3</button>
        <button class="calc-btn operator" onclick="show('+')">+</button>
        <button class="calc-btn zero" onclick="show('0')">0</button>
        <button class="calc-btn" onclick="show('.')">.</button>
        <button class="calc-btn operator" onclick="show('=')">=</button>
      </div>
    </div>
  </body>
  <script>
    let display = document.getElementById("display");
    let numBtn = Number(document.getElementsByClassName("calc-btn"))
    function show(buttonText) {
      if (buttonText === 'AC') {
        display.textContent = '';
      } else if (buttonText === 'C') {
        display.textContent = display.textContent.slice(0, -1);
      } else {
        display.textContent += buttonText;
      }
    }
  </script>
</html>
