<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Calculator</title>
    <style>
      body {
        margin: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background-color: white;
      }

      #calculator {
        width: 280px;
        height: 500px;
        background-color: #000;
        border-radius: 20px;
        padding: 20px;
        display: flex;
        flex-direction: column;
      }

      #display-area {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: flex-end;
        padding: 0 10px 10px;
      }

      #display {
        font-size: 48px;
        color: #fff;
        font-weight: 300;
        text-align: right;
        min-height: 50px;
      }

      #result-display {
        font-size: 24px;
        color: #aaa;
        font-weight: 300;
        text-align: right;
      }

      #button-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 10px;
      }

      button {
        width: 60px;
        height: 60px;
        border: none;
        border-radius: 50%;
        background-color: #333;
        color: #fff;
        font-size: 24px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      button:hover {
        background-color: #555;
      }

      button:active {
        background-color: #777;
      }

      #ac-btn, #c-btn, #percent-btn {
        background-color: #a5a5a5;
        color: #000;
      }

      #ac-btn:hover, #c-btn:hover, #percent-btn:hover {
        background-color: #c5c5c5;
      }

      #ac-btn:active, #c-btn:active, #percent-btn:active {
        background-color: #e5e5e5;
      }

      #operator-btn {
        background-color: #f1a33c;
      }

      #operator-btn:hover {
        background-color: #f3b868;
      }

      #operator-btn:active {
        background-color: #f5c98e;
      }

      #zero-btn {
        grid-column: span 2;
        border-radius: 30px;
        width: 130px;
        justify-content: flex-start;
        padding-left: 22px;
      }
    </style>
  </head>
  <body>
    <div id="calculator">
      <div id="display-area">
        <div id="result-display"></div>
        <div id="display"></div>
      </div>

      <div id="button-grid">
        <button id="ac-btn" onclick="show('AC')">AC</button>
        <button id="c-btn" onclick="show('C')">C</button>
        <button id="percent-btn" onclick="show('%')">%</button>
        <button id="operator-btn" onclick="show('÷')">÷</button>
        <button id="number-btn" onclick="show('7')">7</button>
        <button id="number-btn" onclick="show('8')">8</button>
        <button id="number-btn" onclick="show('9')">9</button>
        <button id="operator-btn" onclick="show('×')">×</button>
        <button id="number-btn" onclick="show('4')">4</button>
        <button id="number-btn" onclick="show('5')">5</button>
        <button id="number-btn" onclick="show('6')">6</button>
        <button id="operator-btn" onclick="show('-')">-</button>
        <button id="number-btn" onclick="show('1')">1</button>
        <button id="number-btn" onclick="show('2')">2</button>
        <button id="number-btn" onclick="show('3')">3</button>
        <button id="operator-btn" onclick="show('+')">+</button>
        <button id="zero-btn" onclick="show('0')">0</button>
        <button id="decimal-btn" onclick="show('.')">.</button>
        <button id="operator-btn" onclick="show('=')">=</button>
      </div>
    </div>
  </body>
  <script>
    let display = document.getElementById("display");
    let numBtn = Number(document.getElementById("number-btn"))
    function show(buttonText) {
      if (buttonText === 'AC') {
        display.textContent = '';
      } else if (buttonText === 'C') {
        display.textContent = display.textContent.slice(0, -1);
      } else {
        display.textContent += buttonText;
      }
    }
  </script>
</html>
